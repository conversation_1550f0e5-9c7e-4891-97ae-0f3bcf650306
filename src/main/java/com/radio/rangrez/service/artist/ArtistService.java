package com.radio.rangrez.service.artist;

import com.radio.rangrez.dto.ArtistDto;
import com.radio.rangrez.dto.ArtistGenreRequest;
import com.radio.rangrez.dto.GenreDto;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface ArtistService {

    ArtistDto createArtist(ArtistDto artistDto);

    List<ArtistDto> getAllArtists();

    ArtistDto getArtistById(Long id);

    ArtistDto updateArtist(Long id, ArtistDto artistDto);

    boolean deleteArtist(Long id);

    // Genre management methods
    List<GenreDto> getArtistGenres(Long artistId);

    ArtistDto addGenresToArtist(Long artistId, ArtistGenreRequest request);

    ArtistDto removeGenreFromArtist(Long artistId, Long genreId);

    ArtistDto updateArtistGenres(Long artistId, ArtistGenreRequest request);

}
