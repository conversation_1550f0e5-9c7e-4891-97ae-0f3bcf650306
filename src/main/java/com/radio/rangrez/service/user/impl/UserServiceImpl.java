package com.radio.rangrez.service.user.impl;

import com.radio.rangrez.constants.Constants;
import com.radio.rangrez.dto.UserDto;
import com.radio.rangrez.dto.auth.CompleteProfileRequest;
import com.radio.rangrez.exception.AuthorizationException;
import com.radio.rangrez.exception.NotFoundException;
import com.radio.rangrez.model.User;
import com.radio.rangrez.repository.UserRepository;
import com.radio.rangrez.service.user.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserRepository userRepository;

    @Override
    public UserDto findByEmail(String email) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            return convertToDto(userOptional.get());
        }
        return null;
    }

    @Override
    public UserDto createUser(String email, String cognitoUserId) {
        User user = new User();
        user.setEmail(email);
        user.setCognitoUserId(cognitoUserId);
        user.setProfileCompleted(false);
        
        User savedUser = userRepository.save(user);
        return convertToDto(savedUser);
    }

    @Override
    public UserDto completeProfile(String email, CompleteProfileRequest request) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            
            // Copy properties from request to user
            BeanUtils.copyProperties(request, user, "id", "email", "cognitoUserId", "created", "updated", "activated");
            user.setProfileCompleted(true);
            
            User updatedUser = userRepository.save(user);
            return convertToDto(updatedUser);
        }
        throw new RuntimeException("User not found with email: " + email);
    }

    @Override
    public UserDto updateProfile(String email, CompleteProfileRequest request) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            User user = userOptional.get();

            // Copy properties from request to user
            BeanUtils.copyProperties(request, user, "id", "email", "cognitoUserId", "created", "updated", "activated");
            user.setProfileCompleted(true);
            user.setUpdated(LocalDateTime.now());

            User updatedUser = userRepository.save(user);
            return convertToDto(updatedUser);
        }
        throw new RuntimeException("User not found with email: " + email);
    }

    @Override
    public boolean isNewUser(String email) {
        return !userRepository.existsByEmail(email);
    }

    @Override
    public UserDto getUserProfile(String email) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isPresent()) {
            return convertToDto(userOptional.get());
        }
        throw new RuntimeException("User not found with email: " + email);
    }

    private UserDto convertToDto(User user) {
        UserDto dto = new UserDto();
        BeanUtils.copyProperties(user, dto);
        return dto;
    }

    public User getCurrentUser() {
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return this.getUserFromAuthentication(authentication);
    }

    public User getUserFromAuthentication(Authentication authentication) {
        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails userDetails) {
            String email = userDetails.getUsername();
            return userRepository.findByEmail(email).orElseThrow(() -> new NotFoundException(NotFoundException.NotFoundType.USER_NOT_FOUND));
        }
        throw new AuthorizationException(Constants.NOT_AUTHORIZE);
    }
}
