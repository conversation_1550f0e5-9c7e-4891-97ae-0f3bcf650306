package com.radio.rangrez.service.coupon.impl;

import com.radio.rangrez.dto.CreateCouponRequest;
import com.radio.rangrez.dto.CouponDto;
import com.radio.rangrez.dto.UpdateCouponPriorityRequest;
import com.radio.rangrez.model.Coupon;
import com.radio.rangrez.repository.CouponRepository;
import com.radio.rangrez.service.coupon.CouponService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CouponServiceImpl implements CouponService {

    @Autowired
    private CouponRepository couponRepository;

    @Override
    public CouponDto createCoupon(CreateCouponRequest request) {
        // Check if coupon code already exists
        if (couponRepository.existsByCouponCodeIgnoreCase(request.getCouponCode())) {
            throw new RuntimeException("Coupon with code '" + request.getCouponCode() + "' already exists");
        }

        // Validate expire time is in the future
        if (request.getExpireTime().isBefore(LocalDateTime.now())) {
            throw new RuntimeException("Expire time must be in the future");
        }

        Coupon coupon = new Coupon();
        BeanUtils.copyProperties(request, coupon, "id", "created", "updated");
        
        Coupon savedCoupon = couponRepository.save(coupon);
        return convertToDto(savedCoupon);
    }

    @Override
    public List<CouponDto> getAllCoupons() {
        List<Coupon> coupons = couponRepository.findAll();
        return coupons.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CouponDto> getActiveCoupons() {
        List<Coupon> coupons = couponRepository.findByActivatedTrueOrderByPriorityAscNameAsc();
        return coupons.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CouponDto> getActiveAndValidCoupons() {
        List<Coupon> coupons = couponRepository.findActiveAndNotExpiredCoupons(LocalDateTime.now());
        return coupons.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CouponDto> getExpiredCoupons() {
        List<Coupon> coupons = couponRepository.findActiveAndExpiredCoupons(LocalDateTime.now());
        return coupons.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public CouponDto getCouponById(Long id) {
        Optional<Coupon> couponOptional = couponRepository.findById(id);
        if (couponOptional.isPresent()) {
            return convertToDto(couponOptional.get());
        }
        throw new RuntimeException("Coupon not found with id: " + id);
    }

    @Override
    public CouponDto getCouponByCode(String couponCode) {
        Optional<Coupon> couponOptional = couponRepository.findByCouponCodeIgnoreCase(couponCode);
        if (couponOptional.isPresent()) {
            return convertToDto(couponOptional.get());
        }
        throw new RuntimeException("Coupon not found with code: " + couponCode);
    }

    @Override
    public CouponDto updateCoupon(Long id, CreateCouponRequest request) {
        Optional<Coupon> couponOptional = couponRepository.findById(id);
        if (couponOptional.isPresent()) {
            Coupon existingCoupon = couponOptional.get();
            
            // Check if coupon code is being changed and if new code already exists
            if (!existingCoupon.getCouponCode().equalsIgnoreCase(request.getCouponCode()) && 
                couponRepository.existsByCouponCodeIgnoreCase(request.getCouponCode())) {
                throw new RuntimeException("Coupon with code '" + request.getCouponCode() + "' already exists");
            }
            
            // Validate expire time is in the future (only for new expire times)
            if (request.getExpireTime() != null && request.getExpireTime().isBefore(LocalDateTime.now())) {
                throw new RuntimeException("Expire time must be in the future");
            }
            
            // Perform partial update
            copyNonNullProperties(request, existingCoupon);
            
            Coupon updatedCoupon = couponRepository.save(existingCoupon);
            return convertToDto(updatedCoupon);
        }
        throw new RuntimeException("Coupon not found with id: " + id);
    }

    @Override
    @Transactional
    public CouponDto updateCouponPriority(Long id, UpdateCouponPriorityRequest request) {
        Optional<Coupon> couponOptional = couponRepository.findById(id);
        if (!couponOptional.isPresent()) {
            throw new RuntimeException("Coupon not found with id: " + id);
        }

        Coupon targetCoupon = couponOptional.get();
        Integer currentPriority = targetCoupon.getPriority();
        Integer newPriority = request.getNewPriority();

        // If priority is not changing, return as is
        if (currentPriority != null && currentPriority.equals(newPriority)) {
            return convertToDto(targetCoupon);
        }

        // Get max priority to validate new priority
        Integer maxPriority = couponRepository.findMaxPriorityForActiveCoupons();
        if (maxPriority == null) {
            maxPriority = 0;
        }

        // Validate new priority is within valid range
        if (newPriority < 1 || newPriority > maxPriority + 1) {
            throw new RuntimeException("Priority must be between 1 and " + (maxPriority + 1));
        }

        // Get all active coupons ordered by priority
        List<Coupon> allActiveCoupons = couponRepository.findByActivatedTrueOrderByPriorityAsc();

        // Remove the target coupon from the list temporarily
        allActiveCoupons.removeIf(coupon -> coupon.getId().equals(id));

        // Reorder priorities
        reorderCouponPriorities(allActiveCoupons, targetCoupon, newPriority);

        return convertToDto(targetCoupon);
    }

    @Override
    public boolean deleteCoupon(Long id) {
        Optional<Coupon> couponOptional = couponRepository.findById(id);
        if (couponOptional.isPresent()) {
            couponRepository.deleteById(id);
            return true;
        }
        throw new RuntimeException("Coupon not found with id: " + id);
    }

    private CouponDto convertToDto(Coupon coupon) {
        CouponDto dto = new CouponDto();
        BeanUtils.copyProperties(coupon, dto);
        
        // Set expired flag
        dto.setExpired(coupon.getExpireTime().isBefore(LocalDateTime.now()));
        
        return dto;
    }

    private void copyNonNullProperties(CreateCouponRequest source, Coupon target) {
        if (source.getName() != null) {
            target.setName(source.getName());
        }
        if (source.getImage() != null) {
            target.setImage(source.getImage());
        }
        if (source.getDescription() != null) {
            target.setDescription(source.getDescription());
        }
        if (source.getShortDescription() != null) {
            target.setShortDescription(source.getShortDescription());
        }
        if (source.getCouponCode() != null) {
            target.setCouponCode(source.getCouponCode());
        }
        if (source.getExpireTime() != null) {
            target.setExpireTime(source.getExpireTime());
        }
        if (source.getPriority() != null) {
            target.setPriority(source.getPriority());
        }
    }

    private void reorderCouponPriorities(List<Coupon> allActiveCoupons, Coupon targetCoupon, Integer newPriority) {
        // Create a new list to hold the reordered coupons
        List<Coupon> reorderedCoupons = new java.util.ArrayList<>();

        // Add coupons in the new order
        boolean targetInserted = false;
        int currentPosition = 1;

        for (Coupon coupon : allActiveCoupons) {
            // If we've reached the position where target coupon should be inserted
            if (currentPosition == newPriority && !targetInserted) {
                targetCoupon.setPriority(currentPosition);
                couponRepository.save(targetCoupon);
                currentPosition++;
                targetInserted = true;
            }

            // Update the current coupon's priority
            coupon.setPriority(currentPosition);
            couponRepository.save(coupon);
            currentPosition++;
        }

        // If target coupon should be at the end
        if (!targetInserted) {
            targetCoupon.setPriority(newPriority);
            couponRepository.save(targetCoupon);
        }
    }
}
