package com.radio.rangrez.service.question.impl;

import com.radio.rangrez.dto.CreateQuestionRequest;
import com.radio.rangrez.dto.QuestionDto;
import com.radio.rangrez.dto.QuestionOptionDto;
import com.radio.rangrez.model.Question;
import com.radio.rangrez.model.QuestionOption;
import com.radio.rangrez.repository.QuestionOptionRepository;
import com.radio.rangrez.repository.QuestionRepository;
import com.radio.rangrez.service.question.QuestionService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class QuestionServiceImpl implements QuestionService {

    @Autowired
    private QuestionRepository questionRepository;

    @Autowired
    private QuestionOptionRepository questionOptionRepository;

    @Override
    @Transactional
    public QuestionDto createQuestion(CreateQuestionRequest request) {
        Question question = new Question();
        BeanUtils.copyProperties(request, question, "id", "created", "updated", "options");

        Question savedQuestion = questionRepository.save(question);

        // Create options
        if (request.getOptions() != null && !request.getOptions().isEmpty()) {
            List<QuestionOption> options = request.getOptions().stream()
                    .map(optionRequest -> {
                        QuestionOption option = new QuestionOption();
                        BeanUtils.copyProperties(optionRequest, option, "id", "created", "updated");
                        option.setQuestion(savedQuestion);
                        return option;
                    })
                    .collect(Collectors.toList());

            questionOptionRepository.saveAll(options);
            savedQuestion.setOptions(options);
        }

        return convertToDto(savedQuestion);
    }

    @Override
    public List<QuestionDto> getAllQuestions() {
        List<Question> questions = questionRepository.findAll();
        return questions.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<QuestionDto> getActiveQuestions() {
        List<Question> questions = questionRepository.findByActivatedTrueOrderByDisplayOrderAscCreatedAsc();
        return questions.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public QuestionDto getQuestionById(Long id) {
        Optional<Question> questionOptional = questionRepository.findById(id);
        if (questionOptional.isPresent()) {
            return convertToDto(questionOptional.get());
        }
        throw new RuntimeException("Question not found with id: " + id);
    }

    @Override
    @Transactional
    public QuestionDto updateQuestion(Long id, CreateQuestionRequest request) {
        Optional<Question> questionOptional = questionRepository.findById(id);
        if (questionOptional.isPresent()) {
            Question existingQuestion = questionOptional.get();

            // Update question properties
            copyNonNullProperties(request, existingQuestion);

            existingQuestion.setUpdated(LocalDateTime.now());

            if (request.getIsRequired() != null) {
                existingQuestion.setIsRequired(request.getIsRequired());
            }


            // Update options if provided
            if (request.getOptions() != null) {
                // Delete existing options
                questionOptionRepository.deleteByQuestionId(id);

                // Create new options
                List<QuestionOption> newOptions = request.getOptions().stream()
                        .map(optionRequest -> {
                            QuestionOption option = new QuestionOption();
                            BeanUtils.copyProperties(optionRequest, option, "id", "created", "updated");
                            option.setQuestion(existingQuestion);
                            return option;
                        })
                        .collect(Collectors.toList());

                questionOptionRepository.saveAll(newOptions);
                existingQuestion.setOptions(newOptions);
            }

            Question updatedQuestion = questionRepository.save(existingQuestion);
            return convertToDto(updatedQuestion);
        }
        throw new RuntimeException("Question not found with id: " + id);
    }

    @Override
    public boolean deleteQuestion(Long id) {
        Optional<Question> questionOptional = questionRepository.findById(id);
        if (questionOptional.isPresent()) {
            questionRepository.deleteById(id);
            return true;
        }
        throw new RuntimeException("Question not found with id: " + id);
    }



    private QuestionDto convertToDto(Question question) {
        QuestionDto dto = new QuestionDto();
        BeanUtils.copyProperties(question, dto, "options");

        // Convert options
        if (question.getOptions() != null) {
            List<QuestionOptionDto> optionDtos = question.getOptions().stream()
                    .map(this::convertOptionToDto)
                    .collect(Collectors.toList());
            dto.setOptions(optionDtos);
        }

        return dto;
    }

    private QuestionOptionDto convertOptionToDto(QuestionOption option) {
        QuestionOptionDto dto = new QuestionOptionDto();
        BeanUtils.copyProperties(option, dto);
        return dto;
    }

    private void copyNonNullProperties(CreateQuestionRequest source, Question target) {
        if (source.getQuestionText() != null) {
            target.setQuestionText(source.getQuestionText());
        }
        if (source.getQuestionType() != null) {
            target.setQuestionType(source.getQuestionType());
        }
        if (source.getDisplayOrder() != null) {
            target.setDisplayOrder(source.getDisplayOrder());
        }
        if (source.getDescription() != null) {
            target.setDescription(source.getDescription());
        }
        target.setIsRequired(source.getIsRequired());
    }
}
