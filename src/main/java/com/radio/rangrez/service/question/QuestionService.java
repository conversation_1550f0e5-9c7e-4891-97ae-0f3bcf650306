package com.radio.rangrez.service.question;

import com.radio.rangrez.dto.CreateQuestionRequest;
import com.radio.rangrez.dto.QuestionDto;

import java.util.List;

public interface QuestionService {
    
    QuestionDto createQuestion(CreateQuestionRequest request);
    
    List<QuestionDto> getAllQuestions();
    
    List<QuestionDto> getActiveQuestions();
    
    QuestionDto getQuestionById(Long id);
    
    QuestionDto updateQuestion(Long id, CreateQuestionRequest request);

    boolean deleteQuestion(Long id);
}
