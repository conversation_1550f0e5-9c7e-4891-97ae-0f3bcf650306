package com.radio.rangrez.service.album.impl;

import com.radio.rangrez.dto.*;
import com.radio.rangrez.exception.NotFoundException;
import com.radio.rangrez.model.Album;
import com.radio.rangrez.model.AlbumSong;
import com.radio.rangrez.model.Artist;
import com.radio.rangrez.model.Song;
import com.radio.rangrez.repository.AlbumRepository;
import com.radio.rangrez.repository.AlbumSongRepository;
import com.radio.rangrez.repository.ArtistRepository;
import com.radio.rangrez.repository.SongRepository;
import com.radio.rangrez.service.album.AlbumService;
import com.radio.rangrez.service.song.SongService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AlbumServiceImpl implements AlbumService {

    @Autowired
    private AlbumRepository albumRepository;

    @Autowired
    private AlbumSongRepository albumSongRepository;

    @Autowired
    private ArtistRepository artistRepository;

    @Autowired
    private SongRepository songRepository;

    @Autowired
    private SongService songService;

    @Override
    @Transactional
    public AlbumDto createAlbum(CreateAlbumRequest request) {
        Album album = new Album();
        BeanUtils.copyProperties(request, album, "id", "primaryArtistId", "songs");

        // Set primary artist
        Artist primaryArtist = artistRepository.findById(request.getPrimaryArtistId())
                .orElseThrow(() -> new NotFoundException("Artist not found with id: " + request.getPrimaryArtistId()));
        album.setPrimaryArtist(primaryArtist);

        Album savedAlbum = albumRepository.save(album);

        // Add songs if provided
        if (request.getSongs() != null && !request.getSongs().isEmpty()) {
            for (CreateAlbumRequest.AlbumSongRequest songRequest : request.getSongs()) {
                addSongToAlbumInternal(savedAlbum, songRequest.getSongId(), songRequest.getTrackNumber());
            }
            updateAlbumStats(savedAlbum);
        }

        return convertToDto(savedAlbum);
    }

    @Override
    public List<AlbumDto> getAllAlbums() {
        return albumRepository.findAll().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<AlbumDto> getActiveAlbums() {
        return albumRepository.findByActivatedTrueOrderByReleaseDateDescTitleAsc().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public AlbumDto getAlbumById(Long id) {
        Album album = albumRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Album not found with id: " + id));
        return convertToDto(album);
    }

    @Override
    @Transactional
    public AlbumDto updateAlbum(Long id, CreateAlbumRequest request) {
        Album album = albumRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Album not found with id: " + id));

        BeanUtils.copyProperties(request, album, "id", "primaryArtistId", "songs");

        // Update primary artist if changed
        if (!album.getPrimaryArtist().getId().equals(request.getPrimaryArtistId())) {
            Artist primaryArtist = artistRepository.findById(request.getPrimaryArtistId())
                    .orElseThrow(() -> new NotFoundException("Artist not found with id: " + request.getPrimaryArtistId()));
            album.setPrimaryArtist(primaryArtist);
        }

        Album savedAlbum = albumRepository.save(album);
        return convertToDto(savedAlbum);
    }

    @Override
    @Transactional
    public boolean deleteAlbum(Long id) {
        Album album = albumRepository.findById(id)
                .orElseThrow(() -> new NotFoundException("Album not found with id: " + id));
        
        album.setActivated(false);
        albumRepository.save(album);
        return true;
    }

    @Override
    public List<SongDto> getAlbumSongs(Long albumId) {
        if (!albumRepository.existsById(albumId)) {
            throw new NotFoundException("Album not found with id: " + albumId);
        }
        
        List<AlbumSong> albumSongs = albumSongRepository.findByAlbumIdOrderByTrackNumberAsc(albumId);
        return albumSongs.stream()
                .map(albumSong -> songService.getSongById(albumSong.getSong().getId()))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public AlbumDto addSongToAlbum(Long albumId, Long songId, Integer trackNumber) {
        Album album = albumRepository.findById(albumId)
                .orElseThrow(() -> new NotFoundException("Album not found with id: " + albumId));
        
        addSongToAlbumInternal(album, songId, trackNumber);
        updateAlbumStats(album);
        
        return convertToDto(album);
    }

    @Override
    @Transactional
    public AlbumDto removeSongFromAlbum(Long albumId, Long songId) {
        Album album = albumRepository.findById(albumId)
                .orElseThrow(() -> new NotFoundException("Album not found with id: " + albumId));
        
        albumSongRepository.deleteByAlbumIdAndSongId(albumId, songId);
        updateAlbumStats(album);
        
        return convertToDto(album);
    }

    @Override
    public List<AlbumDto> getAlbumsByArtist(Long artistId) {
        return albumRepository.findByPrimaryArtistIdAndActivatedTrueOrderByReleaseDateDesc(artistId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SongDto> getAvailableSongsForAlbum(Long albumId) {
        if (!albumRepository.existsById(albumId)) {
            throw new NotFoundException("Album not found with id: " + albumId);
        }

        // Get all active songs
        List<SongDto> allSongs = songService.getAllSongs();

        // Get current album songs
        List<AlbumSong> albumSongs = albumSongRepository.findByAlbumIdOrderByTrackNumberAsc(albumId);
        List<Long> currentSongIds = albumSongs.stream()
                .map(albumSong -> albumSong.getSong().getId())
                .collect(Collectors.toList());

        // Filter out songs already in the album
        return allSongs.stream()
                .filter(song -> !currentSongIds.contains(song.getId()))
                .collect(Collectors.toList());
    }

    private void addSongToAlbumInternal(Album album, Long songId, Integer trackNumber) {
        Song song = songRepository.findById(songId)
                .orElseThrow(() -> new NotFoundException("Song not found with id: " + songId));

        // Check if song already exists in album
        if (albumSongRepository.findByAlbumIdAndSongId(album.getId(), songId).isPresent()) {
            throw new RuntimeException("Song already exists in this album");
        }

        // Check if track number already exists
        if (albumSongRepository.existsByAlbumIdAndTrackNumber(album.getId(), trackNumber)) {
            throw new RuntimeException("Track number " + trackNumber + " already exists in this album");
        }

        AlbumSong albumSong = new AlbumSong();
        albumSong.setAlbum(album);
        albumSong.setSong(song);
        albumSong.setTrackNumber(trackNumber);
        albumSongRepository.save(albumSong);
    }

    private void updateAlbumStats(Album album) {
        List<AlbumSong> albumSongs = albumSongRepository.findByAlbumIdOrderByTrackNumberAsc(album.getId());
        
        album.setTotalTracks(albumSongs.size());
        
        int totalDuration = albumSongs.stream()
                .mapToInt(albumSong -> albumSong.getSong().getDurationSeconds() != null ? 
                         albumSong.getSong().getDurationSeconds() : 0)
                .sum();
        album.setTotalDurationSeconds(totalDuration);
        
        albumRepository.save(album);
    }

    private AlbumDto convertToDto(Album album) {
        AlbumDto dto = new AlbumDto();
        BeanUtils.copyProperties(album, dto, "primaryArtist", "albumSongs");

        // Convert primary artist
        if (album.getPrimaryArtist() != null) {
            ArtistDto artistDto = new ArtistDto();
            BeanUtils.copyProperties(album.getPrimaryArtist(), artistDto);
            dto.setPrimaryArtist(artistDto);
        }

        // Convert album songs
        if (album.getAlbumSongs() != null && !album.getAlbumSongs().isEmpty()) {
            List<AlbumSongDto> songDtos = album.getAlbumSongs().stream()
                    .map(this::convertAlbumSongToDto)
                    .collect(Collectors.toList());
            dto.setSongs(songDtos);
        }

        return dto;
    }

    private AlbumSongDto convertAlbumSongToDto(AlbumSong albumSong) {
        AlbumSongDto dto = new AlbumSongDto();
        dto.setId(albumSong.getId());
        dto.setTrackNumber(albumSong.getTrackNumber());
        
        // Convert song
        SongDto songDto = songService.getSongById(albumSong.getSong().getId());
        dto.setSong(songDto);
        
        return dto;
    }
}
