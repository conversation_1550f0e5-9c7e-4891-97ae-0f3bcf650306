package com.radio.rangrez.service.upload.impl;

import com.radio.rangrez.dto.upload.UploadRequest;
import com.radio.rangrez.dto.upload.UploadResponse;
import com.radio.rangrez.service.upload.UploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
public class UploadServiceImpl implements UploadService {

    @Autowired
    private S3Client s3Client;

    @Autowired
    private S3Presigner s3Presigner;

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    @Value("${aws.s3.base-url}")
    private String baseUrl;

    @Value("${aws.s3.presigned-url-expiration-minutes:15}")
    private int expirationMinutes;

    @Override
    public UploadResponse generatePresignedUrl(UploadRequest request) {
        try {
            String folder = request.getFolder() != null ? request.getFolder().name().toLowerCase() : "misc";

            String fileName = folder + "/" + UUID.randomUUID() + request.getFileExtension();

            String contentType = getContentType(request.getFileExtension());

            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileName)
                    .contentType(contentType)
                    .build();

            // Generate presigned PUT URL
            PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofMinutes(expirationMinutes))
                    .putObjectRequest(putObjectRequest)
                    .build();

            PresignedPutObjectRequest presignedRequest = s3Presigner.presignPutObject(presignRequest);
            String presignedUrl = presignedRequest.url().toString();

            // Build file access URL (for frontend preview/download)
            String fileUrl = baseUrl + "/" + fileName;

            UploadResponse response = new UploadResponse(presignedUrl, fileName, fileUrl, LocalDateTime.now().plusMinutes(expirationMinutes));
            response.setContentType(contentType);
            response.setMaxFileSize(getMaxFileSize(request.getFileExtension()));

            return response;

        } catch (Exception e) {
            throw new RuntimeException("Failed to generate presigned URL: " + e.getMessage(), e);
        }
    }


    @Override
    public String generateFileName(String fileExtension, String folder) {
        LocalDateTime now = LocalDateTime.now();
        
        // Format: year/mm/dd/timestamp_random.extension
        String year = now.format(DateTimeFormatter.ofPattern("yyyy"));
        String month = now.format(DateTimeFormatter.ofPattern("MM"));
        String day = now.format(DateTimeFormatter.ofPattern("dd"));
        String timestamp = now.format(DateTimeFormatter.ofPattern("HHmmss"));
        String milliseconds = String.valueOf(now.getNano() / 1000000); // Convert to milliseconds
        
        // Add folder prefix if provided
        String folderPrefix = (folder != null && !folder.trim().isEmpty()) ? folder + "/" : "";
        
        return String.format("%s%s/%s/%s/%s_%s%s", 
                folderPrefix, year, month, day, timestamp, milliseconds, fileExtension);
    }

    @Override
    public boolean deleteFile(String fileName) {
        try {
            DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileName)
                    .build();

            s3Client.deleteObject(deleteRequest);
            return true;
        } catch (Exception e) {
            throw new RuntimeException("Failed to delete file: " + e.getMessage(), e);
        }
    }

    private String getContentType(String fileExtension) {
        Map<String, String> contentTypes = new HashMap<>();
        
        // Image types
        contentTypes.put(".jpg", "image/jpeg");
        contentTypes.put(".jpeg", "image/jpeg");
        contentTypes.put(".png", "image/png");
        contentTypes.put(".gif", "image/gif");
        
        // Audio types
        contentTypes.put(".mp3", "audio/mpeg");
        contentTypes.put(".wav", "audio/wav");
        contentTypes.put(".flac", "audio/flac");
        
        // Document types
        contentTypes.put(".pdf", "application/pdf");
        contentTypes.put(".doc", "application/msword");
        contentTypes.put(".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        
        return contentTypes.getOrDefault(fileExtension.toLowerCase(), "application/octet-stream");
    }

    private Long getMaxFileSize(String fileExtension) {
        // Return max file size in bytes
        String ext = fileExtension.toLowerCase();
        
        if (ext.matches("\\.(jpg|jpeg|png|gif)")) {
            return 5L * 1024 * 1024; // 5MB for images
        } else if (ext.matches("\\.(mp3|wav|flac)")) {
            return 50L * 1024 * 1024; // 50MB for audio
        } else if (ext.matches("\\.(pdf|doc|docx)")) {
            return 10L * 1024 * 1024; // 10MB for documents
        }
        
        return 5L * 1024 * 1024; // Default 5MB
    }
}
