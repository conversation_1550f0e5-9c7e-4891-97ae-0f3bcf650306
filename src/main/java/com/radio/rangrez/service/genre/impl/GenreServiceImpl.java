package com.radio.rangrez.service.genre.impl;

import com.radio.rangrez.dto.CreateGenreRequest;
import com.radio.rangrez.dto.GenreDto;
import com.radio.rangrez.model.Genre;
import com.radio.rangrez.repository.GenreRepository;
import com.radio.rangrez.service.genre.GenreService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class GenreServiceImpl implements GenreService {

    @Autowired
    private GenreRepository genreRepository;

    @Override
    public GenreDto createGenre(CreateGenreRequest request) {
        // Check if genre already exists
        if (genreRepository.existsByNameIgnoreCase(request.getName())) {
            throw new RuntimeException("Genre with name '" + request.getName() + "' already exists");
        }

        Genre genre = new Genre();
        BeanUtils.copyProperties(request, genre, "id", "created", "updated");
        
        Genre savedGenre = genreRepository.save(genre);
        return convertToDto(savedGenre);
    }

    @Override
    public List<GenreDto> getAllGenres() {
        List<Genre> genres = genreRepository.findAll();
        return genres.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<GenreDto> getActiveGenres() {
        List<Genre> genres = genreRepository.findByActivatedTrueOrderByDisplayOrderAscNameAsc();
        return genres.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public GenreDto getGenreById(Long id) {
        Optional<Genre> genreOptional = genreRepository.findById(id);
        if (genreOptional.isPresent()) {
            return convertToDto(genreOptional.get());
        }
        throw new RuntimeException("Genre not found with id: " + id);
    }

    @Override
    public GenreDto updateGenre(Long id, CreateGenreRequest request) {
        Optional<Genre> genreOptional = genreRepository.findById(id);
        if (genreOptional.isPresent()) {
            Genre existingGenre = genreOptional.get();
            
            // Check if name is being changed and if new name already exists
            if (!existingGenre.getName().equalsIgnoreCase(request.getName()) && 
                genreRepository.existsByNameIgnoreCase(request.getName())) {
                throw new RuntimeException("Genre with name '" + request.getName() + "' already exists");
            }
            
            // Perform partial update
            copyNonNullProperties(request, existingGenre);
            
            Genre updatedGenre = genreRepository.save(existingGenre);
            return convertToDto(updatedGenre);
        }
        throw new RuntimeException("Genre not found with id: " + id);
    }

    @Override
    public boolean deleteGenre(Long id) {
        Optional<Genre> genreOptional = genreRepository.findById(id);
        if (genreOptional.isPresent()) {
            genreRepository.deleteById(id);
            return true;
        }
        throw new RuntimeException("Genre not found with id: " + id);
    }

    private GenreDto convertToDto(Genre genre) {
        GenreDto dto = new GenreDto();
        BeanUtils.copyProperties(genre, dto);
        return dto;
    }

    private void copyNonNullProperties(CreateGenreRequest source, Genre target) {
        if (source.getName() != null) {
            target.setName(source.getName());
        }
        if (source.getDescription() != null) {
            target.setDescription(source.getDescription());
        }
        if (source.getIconUrl() != null) {
            target.setIconUrl(source.getIconUrl());
        }
        if (source.getColorCode() != null) {
            target.setColorCode(source.getColorCode());
        }
        if (source.getDisplayOrder() != null) {
            target.setDisplayOrder(source.getDisplayOrder());
        }
    }
}
