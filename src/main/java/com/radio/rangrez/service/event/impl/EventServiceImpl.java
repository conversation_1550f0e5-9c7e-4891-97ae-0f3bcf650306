package com.radio.rangrez.service.event.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.services.calendar.model.Event;
import com.radio.rangrez.dto.event.CreateEventRequest;
import com.radio.rangrez.dto.event.EventDto;
import com.radio.rangrez.dto.event.EventResponse;
import com.radio.rangrez.dto.event.UpdateEventRequest;
import com.radio.rangrez.exception.NotFoundException;
import com.radio.rangrez.repository.EventRepository;
import com.radio.rangrez.service.event.EventService;
import com.radio.rangrez.service.event.GoogleCalendarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EventServiceImpl implements EventService {

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private GoogleCalendarService googleCalendarService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public EventResponse createEvent(CreateEventRequest request, String userEmail) {
        try {
            // Create event in local database first
            com.radio.rangrez.model.Event event = new com.radio.rangrez.model.Event();
            BeanUtils.copyProperties(request, event, "attendees");
            event.setCreatedBy(userEmail);
            
            // Convert attendees list to JSON string
            if (request.getAttendees() != null && !request.getAttendees().isEmpty()) {
                try {
                    String attendeesJson = objectMapper.writeValueAsString(request.getAttendees());
                    event.setAttendees(attendeesJson);
                } catch (JsonProcessingException e) {
                    log.warn("Failed to serialize attendees: {}", e.getMessage());
                }
            }

            com.radio.rangrez.model.Event savedEvent = eventRepository.save(event);

            // Try to create event in Google Calendar
            try {
                Event googleEvent = googleCalendarService.createCalendarEvent(request, userEmail);
                savedEvent.setGoogleCalendarEventId(googleEvent.getId());
                savedEvent.setGoogleCalendarHtmlLink(googleEvent.getHtmlLink());
                savedEvent = eventRepository.save(savedEvent);

                EventDto eventDto = convertToDto(savedEvent);
                return new EventResponse(eventDto, googleEvent.getHtmlLink());

            } catch (IOException e) {
                log.error("Failed to create Google Calendar event: {}", e.getMessage());
                EventDto eventDto = convertToDto(savedEvent);
                return new EventResponse(eventDto, "Event created locally but failed to sync with Google Calendar: " + e.getMessage(), false);
            }

        } catch (Exception e) {
            log.error("Error creating event: {}", e.getMessage());
            throw new RuntimeException("Failed to create event: " + e.getMessage());
        }
    }

    @Override
    public EventResponse updateEvent(Long eventId, UpdateEventRequest request, String userEmail) {
        Optional<com.radio.rangrez.model.Event> eventOptional = eventRepository.findById(eventId);
        if (eventOptional.isEmpty()) {
            throw new NotFoundException(NotFoundException.NotFoundType.EVENT_NOT_FOUND);
        }

        com.radio.rangrez.model.Event event = eventOptional.get();
        
        // Check if user has permission to update this event
        if (!event.getCreatedBy().equals(userEmail)) {
            throw new RuntimeException("You don't have permission to update this event");
        }

        try {
            // Update local event
            updateEventFields(event, request);
            com.radio.rangrez.model.Event updatedEvent = eventRepository.save(event);

            // Try to update Google Calendar event if it exists
            if (event.getGoogleCalendarEventId() != null) {
                try {
                    String calendarId = event.getCalendarId() != null ? event.getCalendarId() : "primary";
                    Event googleEvent = googleCalendarService.updateCalendarEvent(
                            event.getGoogleCalendarEventId(), request, calendarId);
                    updatedEvent.setGoogleCalendarHtmlLink(googleEvent.getHtmlLink());
                    updatedEvent = eventRepository.save(updatedEvent);

                    EventDto eventDto = convertToDto(updatedEvent);
                    return new EventResponse(eventDto, googleEvent.getHtmlLink());

                } catch (IOException e) {
                    log.error("Failed to update Google Calendar event: {}", e.getMessage());
                    EventDto eventDto = convertToDto(updatedEvent);
                    return new EventResponse(eventDto, "Event updated locally but failed to sync with Google Calendar: " + e.getMessage(), false);
                }
            } else {
                EventDto eventDto = convertToDto(updatedEvent);
                return new EventResponse(eventDto, "Event updated locally (not synced with Google Calendar)", false);
            }

        } catch (Exception e) {
            log.error("Error updating event: {}", e.getMessage());
            throw new RuntimeException("Failed to update event: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteEvent(Long eventId, String userEmail) {
        Optional<com.radio.rangrez.model.Event> eventOptional = eventRepository.findById(eventId);
        if (eventOptional.isEmpty()) {
            throw new NotFoundException(NotFoundException.NotFoundType.EVENT_NOT_FOUND);
        }

        com.radio.rangrez.model.Event event = eventOptional.get();
        
        // Check if user has permission to delete this event
        if (!event.getCreatedBy().equals(userEmail)) {
            throw new RuntimeException("You don't have permission to delete this event");
        }

        try {
            // Try to delete from Google Calendar first
            if (event.getGoogleCalendarEventId() != null) {
                try {
                    String calendarId = event.getCalendarId() != null ? event.getCalendarId() : "primary";
                    googleCalendarService.deleteCalendarEvent(event.getGoogleCalendarEventId(), calendarId);
                } catch (IOException e) {
                    log.warn("Failed to delete Google Calendar event, but continuing with local deletion: {}", e.getMessage());
                }
            }

            // Soft delete - set activated to false
            event.setActivated(false);
            eventRepository.save(event);
            return true;

        } catch (Exception e) {
            log.error("Error deleting event: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public EventDto getEventById(Long eventId) {
        Optional<com.radio.rangrez.model.Event> eventOptional = eventRepository.findById(eventId);
        if (eventOptional.isEmpty() || !eventOptional.get().isActivated()) {
            throw new NotFoundException(NotFoundException.NotFoundType.EVENT_NOT_FOUND);
        }
        return convertToDto(eventOptional.get());
    }

    @Override
    public List<EventDto> getAllEvents() {
        return eventRepository.findByActivatedTrueOrderByStartDateTimeAsc()
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<EventDto> getEventsByUser(String userEmail) {
        return eventRepository.findByCreatedByAndActivatedTrueOrderByStartDateTimeAsc(userEmail)
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<EventDto> getUpcomingEvents() {
        return eventRepository.findUpcomingEvents(LocalDateTime.now())
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<EventDto> getPastEvents() {
        return eventRepository.findPastEvents(LocalDateTime.now())
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<EventDto> getEventsBetweenDates(LocalDateTime startDate, LocalDateTime endDate) {
        return eventRepository.findEventsBetweenDates(startDate, endDate)
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public EventResponse syncEventWithGoogleCalendar(Long eventId, String userEmail) {
        Optional<com.radio.rangrez.model.Event> eventOptional = eventRepository.findById(eventId);
        if (eventOptional.isEmpty()) {
            throw new NotFoundException(NotFoundException.NotFoundType.EVENT_NOT_FOUND);
        }

        com.radio.rangrez.model.Event event = eventOptional.get();
        
        // Check if user has permission
        if (!event.getCreatedBy().equals(userEmail)) {
            throw new RuntimeException("You don't have permission to sync this event");
        }

        try {
            CreateEventRequest request = new CreateEventRequest();
            BeanUtils.copyProperties(event, request, "attendees");
            
            // Convert attendees JSON back to list
            if (event.getAttendees() != null && !event.getAttendees().isEmpty()) {
                try {
                    List<String> attendeesList = objectMapper.readValue(event.getAttendees(), new TypeReference<List<String>>() {});
                    request.setAttendees(attendeesList);
                } catch (JsonProcessingException e) {
                    log.warn("Failed to deserialize attendees: {}", e.getMessage());
                }
            }

            Event googleEvent = googleCalendarService.createCalendarEvent(request, userEmail);
            event.setGoogleCalendarEventId(googleEvent.getId());
            event.setGoogleCalendarHtmlLink(googleEvent.getHtmlLink());
            com.radio.rangrez.model.Event updatedEvent = eventRepository.save(event);

            EventDto eventDto = convertToDto(updatedEvent);
            return new EventResponse(eventDto, googleEvent.getHtmlLink());

        } catch (IOException e) {
            log.error("Failed to sync event with Google Calendar: {}", e.getMessage());
            throw new RuntimeException("Failed to sync with Google Calendar: " + e.getMessage());
        }
    }

    private void updateEventFields(com.radio.rangrez.model.Event event, UpdateEventRequest request) {
        if (request.getTitle() != null) {
            event.setTitle(request.getTitle());
        }
        if (request.getDescription() != null) {
            event.setDescription(request.getDescription());
        }
        if (request.getStartDateTime() != null) {
            event.setStartDateTime(request.getStartDateTime());
        }
        if (request.getEndDateTime() != null) {
            event.setEndDateTime(request.getEndDateTime());
        }
        if (request.getLiveRadioUrl() != null) {
            event.setLiveRadioUrl(request.getLiveRadioUrl());
        }
        if (request.getLocation() != null) {
            event.setLocation(request.getLocation());
        }
        if (request.getAttendees() != null) {
            try {
                String attendeesJson = objectMapper.writeValueAsString(request.getAttendees());
                event.setAttendees(attendeesJson);
            } catch (JsonProcessingException e) {
                log.warn("Failed to serialize attendees: {}", e.getMessage());
            }
        }
        if (request.getEventStatus() != null) {
            event.setEventStatus(request.getEventStatus());
        }
        if (request.getTimezone() != null) {
            event.setTimezone(request.getTimezone());
        }
    }

    private EventDto convertToDto(com.radio.rangrez.model.Event event) {
        EventDto dto = new EventDto();
        BeanUtils.copyProperties(event, dto, "attendees");
        
        // Convert attendees JSON to list
        if (event.getAttendees() != null && !event.getAttendees().isEmpty()) {
            try {
                List<String> attendeesList = objectMapper.readValue(event.getAttendees(), new TypeReference<List<String>>() {});
                dto.setAttendees(attendeesList);
            } catch (JsonProcessingException e) {
                log.warn("Failed to deserialize attendees: {}", e.getMessage());
            }
        }
        
        return dto;
    }
}
