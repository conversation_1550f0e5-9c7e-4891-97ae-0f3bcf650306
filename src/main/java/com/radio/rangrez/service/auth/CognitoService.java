package com.radio.rangrez.service.auth;

import com.radio.rangrez.utils.CognitoUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.cognitoidentityprovider.CognitoIdentityProviderClient;
import software.amazon.awssdk.services.cognitoidentityprovider.model.*;

import java.util.List;

@Service
public class CognitoService {

    private final CognitoIdentityProviderClient cognitoClient;

    @Value("${aws.cognito.client-id}")
    private String clientId;

    @Value("${aws.cognito.user-pool-id}")
    private String userPoolId;

    @Value("${aws.cognito.client-secret}")
    private String clientSecret;

    public CognitoService(CognitoIdentityProviderClient cognitoClient) {
        this.cognitoClient = cognitoClient;
    }

    // 1. Sign Up (Triggers OTP)
    public boolean sendOtp(String email, String password) {
        try {
            // Use fallback dummy password if none is provided
            if (password == null || password.trim().isEmpty()) {
                password = "User@123";
            }

            // Try to sign up the user
            SignUpRequest signUpRequest = SignUpRequest.builder()
                    .clientId(clientId)
                    .username(email)
                    .password(password)
                    .secretHash(CognitoUtil.calculateSecretHash(clientId, clientSecret, email))
                    .userAttributes(
                            AttributeType.builder().name("email").value(email).build()
                    )
                    .build();

            cognitoClient.signUp(signUpRequest);
            return true;

        } catch (UsernameExistsException e) {
            // User already exists, fetch their status
            try {
                AdminGetUserResponse response = cognitoClient.adminGetUser(
                        AdminGetUserRequest.builder()
                                .userPoolId(userPoolId)
                                .username(email)
                                .build()
                );

                String status = response.userStatusAsString();
                System.out.println("User exists with status: " + status);

                if ("UNCONFIRMED".equals(status)) {
                    // Resend confirmation code
                    cognitoClient.resendConfirmationCode(
                            ResendConfirmationCodeRequest.builder()
                                    .clientId(clientId)
                                    .username(email)
                                    .secretHash(CognitoUtil.calculateSecretHash(clientId, clientSecret, email))
                                    .build()
                    );
                    return true;
                } else if ("CONFIRMED".equals(status)) {
                    // Trigger ForgotPassword to send code to CONFIRMED users
                    cognitoClient.forgotPassword(
                            ForgotPasswordRequest.builder()
                                    .clientId(clientId)
                                    .username(email)
                                    .secretHash(CognitoUtil.calculateSecretHash(clientId, clientSecret, email))
                                    .build()
                    );
                    return true;
                } else {
                    return false;
                }

            } catch (UserNotFoundException ex) {
                return false;
            } catch (CognitoIdentityProviderException ex) {
                throw new RuntimeException("Failed to send code to existing user: " + ex.awsErrorDetails().errorMessage(), ex);
            }

        } catch (CognitoIdentityProviderException e) {
            throw new RuntimeException("Failed to send OTP: " + e.awsErrorDetails().errorMessage(), e);
        }
    }



    // 2. Confirm Sign Up (Verify OTP)
    public boolean verifyOtp(String email, String otp) {
        try {
            // Step 1: Get user status from Cognito
            AdminGetUserResponse userResponse = cognitoClient.adminGetUser(
                    AdminGetUserRequest.builder()
                            .userPoolId(userPoolId)
                            .username(email)
                            .build()
            );

            String status = userResponse.userStatusAsString();

            if ("UNCONFIRMED".equals(status)) {
                // Use ConfirmSignUp if user is not confirmed yet
                ConfirmSignUpRequest confirmRequest = ConfirmSignUpRequest.builder()
                        .clientId(clientId)
                        .username(email)
                        .confirmationCode(otp)
                        .secretHash(CognitoUtil.calculateSecretHash(clientId, clientSecret, email))
                        .build();

                cognitoClient.confirmSignUp(confirmRequest);
                return true;

            } else if ("CONFIRMED".equals(status)) {
                // Use ConfirmForgotPassword to verify OTP sent via forgotPassword()
                ConfirmForgotPasswordRequest confirmForgotPasswordRequest = ConfirmForgotPasswordRequest.builder()
                        .clientId(clientId)
                        .username(email)
                        .confirmationCode(otp)
                        .password("User@123") // Reuse dummy password
                        .secretHash(CognitoUtil.calculateSecretHash(clientId, clientSecret, email))
                        .build();

                cognitoClient.confirmForgotPassword(confirmForgotPasswordRequest);
                return true;

            } else {
                throw new RuntimeException("User is in unexpected status: " + status);
            }

        } catch (CodeMismatchException e) {
            // Wrong OTP
            return false;

        } catch (ExpiredCodeException e) {
            throw new RuntimeException("OTP expired. Please request a new one.", e);

        } catch (UserNotFoundException e) {
            throw new RuntimeException("User does not exist: " + email, e);

        } catch (CognitoIdentityProviderException e) {
            throw new RuntimeException("Failed to verify OTP: " + e.awsErrorDetails().errorMessage(), e);
        }
    }


    // 3. Sign In (Login)
    public String signIn(String email, String password) {
        try {
            InitiateAuthRequest authRequest = InitiateAuthRequest.builder()
                    .authFlow(AuthFlowType.USER_PASSWORD_AUTH)
                    .clientId(clientId)
                    .authParameters(
                            java.util.Map.of(
                                    "USERNAME", email,
                                    "PASSWORD", password
                            )
                    )
                    .build();

            InitiateAuthResponse response = cognitoClient.initiateAuth(authRequest);
            return response.authenticationResult().idToken(); // or accessToken(), refreshToken()
        } catch (CognitoIdentityProviderException e) {
            throw new RuntimeException("Failed to sign in: " + e.awsErrorDetails().errorMessage(), e);
        }
    }

    // 4. Forgot Password (Triggers OTP)
    public void forgotPassword(String email) {
        try {
            ForgotPasswordRequest request = ForgotPasswordRequest.builder()
                    .clientId(clientId)
                    .username(email)
                    .build();

            cognitoClient.forgotPassword(request);
        } catch (CognitoIdentityProviderException e) {
            throw new RuntimeException("Failed to send password reset OTP: " + e.awsErrorDetails().errorMessage(), e);
        }
    }

    // 5. Confirm Forgot Password (Verify OTP + Set New Password)
    public void confirmForgotPassword(String email, String otp, String newPassword) {
        try {
            ConfirmForgotPasswordRequest request = ConfirmForgotPasswordRequest.builder()
                    .clientId(clientId)
                    .username(email)
                    .confirmationCode(otp)
                    .password(newPassword)
                    .build();

            cognitoClient.confirmForgotPassword(request);
        } catch (CognitoIdentityProviderException e) {
            throw new RuntimeException("Failed to confirm password reset: " + e.awsErrorDetails().errorMessage(), e);
        }
    }

    public String getCognitoUserId(String email) {
        try {
            AdminGetUserRequest getUserRequest = AdminGetUserRequest.builder()
                    .userPoolId(userPoolId)
                    .username(email)
                    .build();

            AdminGetUserResponse getUserResponse = cognitoClient.adminGetUser(getUserRequest);
            return getUserResponse.username();
        } catch (Exception e) {
            throw new RuntimeException("Failed to get Cognito user ID: " + e.getMessage(), e);
        }
    }
}
