package com.radio.rangrez.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

public class CognitoUtil {
    public static String calculateSecretHash(String userPoolClientId, String userPoolClientSecret, String username) {
        try {
            final String HMAC_ALGORITHM = "HmacSHA256";
            SecretKeySpec signingKey = new SecretKeySpec(
                    userPoolClientSecret.getBytes("UTF-8"), HMAC_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_ALGORITHM);
            mac.init(signingKey);
            mac.update(username.getBytes("UTF-8"));
            byte[] rawHmac = mac.doFinal(userPoolClientId.getBytes("UTF-8"));
            return Base64.getEncoder().encodeToString(rawHmac);
        } catch (Exception e) {
            throw new RuntimeException("Error while calculating secret hash", e);
        }
    }
}
