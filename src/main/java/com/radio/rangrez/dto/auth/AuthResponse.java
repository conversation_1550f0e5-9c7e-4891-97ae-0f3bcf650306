package com.radio.rangrez.dto.auth;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthResponse {

    private String token;
    private String email;
    private boolean isNewUser;
    private boolean profileCompleted;
    private String message;

    public AuthResponse(String token, String email, boolean isNewUser, boolean profileCompleted) {
        this.token = token;
        this.email = email;
        this.isNewUser = isNewUser;
        this.profileCompleted = profileCompleted;
    }

}
