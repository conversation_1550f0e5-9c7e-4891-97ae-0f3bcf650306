package com.radio.rangrez.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateCouponRequest {

    @NotBlank(message = "Coupon name is required")
    private String name;

    private String image;

    private String description;

    private String shortDescription;

    @NotBlank(message = "Coupon code is required")
    private String couponCode;

    @NotNull(message = "Expire time is required")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime expireTime;

    private Integer priority;
}
