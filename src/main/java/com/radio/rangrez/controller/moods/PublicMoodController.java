package com.radio.rangrez.controller.moods;

import com.radio.rangrez.dto.MoodDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.mood.MoodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/public/moods")
@RestController
public class PublicMoodController {

    @Autowired
    private MoodService moodService;

    @GetMapping
    public RestResponse getActiveMoods() {
        try {
            List<MoodDto> moods = moodService.getActiveMoods();
            return new RestResponse(true, moods);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getMoodById(@PathVariable Long id) {
        try {
            MoodDto mood = moodService.getMoodById(id);
            return new RestResponse(true, mood);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }
}
