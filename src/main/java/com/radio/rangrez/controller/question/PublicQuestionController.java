package com.radio.rangrez.controller.question;

import com.radio.rangrez.dto.QuestionDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.question.QuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/public/questions")
@RestController
public class PublicQuestionController {

    @Autowired
    private QuestionService questionService;

    @GetMapping
    public RestResponse getActiveQuestions() {
        try {
            List<QuestionDto> questions = questionService.getActiveQuestions();
            return new RestResponse(true, questions);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }


}
