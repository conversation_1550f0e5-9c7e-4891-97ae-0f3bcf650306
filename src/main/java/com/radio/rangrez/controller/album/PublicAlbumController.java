package com.radio.rangrez.controller.album;

import com.radio.rangrez.dto.AlbumDto;
import com.radio.rangrez.dto.SongDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.album.AlbumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/public/albums")
@RestController
public class PublicAlbumController {

    @Autowired
    private AlbumService albumService;

    @GetMapping
    public RestResponse getActiveAlbums() {
        try {
            List<AlbumDto> albums = albumService.getActiveAlbums();
            return new RestResponse(true, albums);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getAlbumById(@PathVariable Long id) {
        try {
            AlbumDto album = albumService.getAlbumById(id);
            return new RestResponse(true, album);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}/songs")
    public RestResponse getAlbumSongs(@PathVariable Long id) {
        try {
            List<SongDto> songs = albumService.getAlbumSongs(id);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/artist/{artistId}")
    public RestResponse getAlbumsByArtist(@PathVariable Long artistId) {
        try {
            List<AlbumDto> albums = albumService.getAlbumsByArtist(artistId);
            return new RestResponse(true, albums);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }
}
