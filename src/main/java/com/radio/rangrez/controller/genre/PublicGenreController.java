package com.radio.rangrez.controller.genre;

import com.radio.rangrez.dto.GenreDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.genre.GenreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/public/genres")
@RestController
public class PublicGenreController {

    @Autowired
    private GenreService genreService;

    @GetMapping
    public RestResponse getActiveGenres() {
        try {
            List<GenreDto> genres = genreService.getActiveGenres();
            return new RestResponse(true, genres);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getGenreById(@PathVariable Long id) {
        try {
            GenreDto genre = genreService.getGenreById(id);
            return new RestResponse(true, genre);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }
}
