package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.CreateMoodRequest;
import com.radio.rangrez.dto.MoodDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.mood.MoodService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/moods")
@RestController
public class MoodController {

    @Autowired
    private MoodService moodService;

    @PostMapping
    public RestResponse createMood(@Valid @RequestBody CreateMoodRequest request) {
        try {
            MoodDto createdMood = moodService.createMood(request);
            return new RestResponse(true, createdMood);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping
    public RestResponse getAllMoods() {
        try {
            List<MoodDto> moods = moodService.getAllMoods();
            return new RestResponse(true, moods);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/active")
    public RestResponse getActiveMoods() {
        try {
            List<MoodDto> moods = moodService.getActiveMoods();
            return new RestResponse(true, moods);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getMoodById(@PathVariable Long id) {
        try {
            MoodDto mood = moodService.getMoodById(id);
            return new RestResponse(true, mood);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public RestResponse updateMood(@PathVariable Long id, @Valid @RequestBody CreateMoodRequest request) {
        try {
            MoodDto updatedMood = moodService.updateMood(id, request);
            return new RestResponse(true, updatedMood);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteMood(@PathVariable Long id) {
        try {
            boolean deleted = moodService.deleteMood(id);
            return new RestResponse(deleted, deleted ? "Mood deleted successfully" : "Failed to delete mood");
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }
}
