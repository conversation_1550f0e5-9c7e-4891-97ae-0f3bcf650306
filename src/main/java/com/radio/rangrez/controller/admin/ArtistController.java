package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.ArtistDto;
import com.radio.rangrez.dto.ArtistGenreRequest;
import com.radio.rangrez.dto.GenreDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.artist.ArtistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/artist")
@RestController
public class ArtistController {

    @Autowired
    private ArtistService artistService;

    @PostMapping
    public RestResponse createArtist(@RequestBody ArtistDto artistDto){
        try {
            ArtistDto createdArtist = artistService.createArtist(artistDto);
            return new RestResponse(true, createdArtist);
        } catch (Exception e) {
            return new RestResponse(false);
        }
    }

    @GetMapping
    public RestResponse getAllArtists(){
        try {
            List<ArtistDto> artists = artistService.getAllArtists();
            return new RestResponse(true, artists);
        } catch (Exception e) {
            return new RestResponse(false);
        }
    }

    @GetMapping("/{id}")
    public RestResponse getArtistById(@PathVariable Long id){
        try {
            ArtistDto artist = artistService.getArtistById(id);
            return new RestResponse(true, artist);
        } catch (Exception e) {
            return new RestResponse(false);
        }
    }

    @PutMapping("/{id}")
    public RestResponse updateArtist(@PathVariable Long id, @RequestBody ArtistDto artistDto){
        try {
            ArtistDto updatedArtist = artistService.updateArtist(id, artistDto);
            return new RestResponse(true, updatedArtist);
        } catch (Exception e) {
            return new RestResponse(false);
        }
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteArtist(@PathVariable Long id){
        try {
            boolean deleted = artistService.deleteArtist(id);
            return new RestResponse(deleted, deleted ? "Artist deleted successfully" : "Failed to delete artist");
        } catch (Exception e) {
            return new RestResponse(false);
        }
    }

    // Genre management endpoints
    @GetMapping("/{id}/genres")
    public RestResponse getArtistGenres(@PathVariable Long id) {
        try {
            List<GenreDto> genres = artistService.getArtistGenres(id);
            return new RestResponse(true, genres);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PostMapping("/{id}/genres")
    public RestResponse addGenresToArtist(@PathVariable Long id, @RequestBody ArtistGenreRequest request) {
        try {
            ArtistDto updatedArtist = artistService.addGenresToArtist(id, request);
            return new RestResponse(true, updatedArtist);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/{id}/genres")
    public RestResponse updateArtistGenres(@PathVariable Long id, @RequestBody ArtistGenreRequest request) {
        try {
            ArtistDto updatedArtist = artistService.updateArtistGenres(id, request);
            return new RestResponse(true, updatedArtist);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}/genres/{genreId}")
    public RestResponse removeGenreFromArtist(@PathVariable Long id, @PathVariable Long genreId) {
        try {
            ArtistDto updatedArtist = artistService.removeGenreFromArtist(id, genreId);
            return new RestResponse(true, updatedArtist);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

}
