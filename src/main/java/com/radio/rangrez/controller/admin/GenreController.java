package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.CreateGenreRequest;
import com.radio.rangrez.dto.GenreDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.genre.GenreService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/genres")
@RestController
public class GenreController {

    @Autowired
    private GenreService genreService;

    @PostMapping
    public RestResponse createGenre(@Valid @RequestBody CreateGenreRequest request) {
        try {
            GenreDto createdGenre = genreService.createGenre(request);
            return new RestResponse(true, createdGenre);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping
    public RestResponse getAllGenres() {
        try {
            List<GenreDto> genres = genreService.getAllGenres();
            return new RestResponse(true, genres);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/active")
    public RestResponse getActiveGenres() {
        try {
            List<GenreDto> genres = genreService.getActiveGenres();
            return new RestResponse(true, genres);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getGenreById(@PathVariable Long id) {
        try {
            GenreDto genre = genreService.getGenreById(id);
            return new RestResponse(true, genre);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public RestResponse updateGenre(@PathVariable Long id, @Valid @RequestBody CreateGenreRequest request) {
        try {
            GenreDto updatedGenre = genreService.updateGenre(id, request);
            return new RestResponse(true, updatedGenre);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteGenre(@PathVariable Long id) {
        try {
            boolean deleted = genreService.deleteGenre(id);
            return new RestResponse(deleted, deleted ? "Genre deleted successfully" : "Failed to delete genre");
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }
}
