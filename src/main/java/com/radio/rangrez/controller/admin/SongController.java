package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.CreateSongRequest;
import com.radio.rangrez.dto.SongDto;
import com.radio.rangrez.dto.UpdateSongArtistsRequest;
import com.radio.rangrez.dto.UpdateSongGenresRequest;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.song.SongService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/songs")
@RestController
public class SongController {

    @Autowired
    private SongService songService;

    @PostMapping
    public RestResponse createSong(@Valid @RequestBody CreateSongRequest request) {
        try {
            SongDto createdSong = songService.createSong(request);
            return new RestResponse(true, createdSong);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping
    public RestResponse getAllSongs() {
        try {
            List<SongDto> songs = songService.getAllSongs();
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/paginated")
    public RestResponse getAllSongs(Pageable pageable) {
        try {
            Page<SongDto> songs = songService.getAllSongs(pageable);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/active")
    public RestResponse getActiveSongs() {
        try {
            List<SongDto> songs = songService.getActiveSongs();
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getSongById(@PathVariable Long id) {
        try {
            SongDto song = songService.getSongById(id);
            return new RestResponse(true, song);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public RestResponse updateSong(@PathVariable Long id, @Valid @RequestBody CreateSongRequest request) {
        try {
            SongDto updatedSong = songService.updateSong(id, request);
            return new RestResponse(true, updatedSong);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/{id}/genres")
    public RestResponse updateSongGenres(@PathVariable Long id, @Valid @RequestBody UpdateSongGenresRequest request) {
        try {
            SongDto updatedSong = songService.updateSongGenres(id, request);
            return new RestResponse(true, updatedSong);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/{id}/artists")
    public RestResponse updateSongArtists(@PathVariable Long id, @Valid @RequestBody UpdateSongArtistsRequest request) {
        try {
            SongDto updatedSong = songService.updateSongArtists(id, request);
            return new RestResponse(true, updatedSong);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteSong(@PathVariable Long id) {
        try {
            boolean deleted = songService.deleteSong(id);
            return new RestResponse(deleted, deleted ? "Song deleted successfully" : "Failed to delete song");
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/search")
    public RestResponse searchSongs(@RequestParam String title) {
        try {
            List<SongDto> songs = songService.searchSongsByTitle(title);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/album/{albumName}")
    public RestResponse getSongsByAlbum(@PathVariable String albumName) {
        try {
            List<SongDto> songs = songService.getSongsByAlbum(albumName);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/language/{language}")
    public RestResponse getSongsByLanguage(@PathVariable String language) {
        try {
            List<SongDto> songs = songService.getSongsByLanguage(language);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/artist/{artistId}")
    public RestResponse getSongsByArtist(@PathVariable Long artistId) {
        try {
            List<SongDto> songs = songService.getSongsByArtist(artistId);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/genre/{genreId}")
    public RestResponse getSongsByGenre(@PathVariable Long genreId) {
        try {
            List<SongDto> songs = songService.getSongsByGenre(genreId);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/mood/{moodId}")
    public RestResponse getSongsByMood(@PathVariable Long moodId) {
        try {
            List<SongDto> songs = songService.getSongsByMood(moodId);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PostMapping("/{id}/rate")
    public RestResponse rateSong(@PathVariable Long id, @RequestParam Double rating) {
        try {
            SongDto updatedSong = songService.rateSong(id, rating);
            return new RestResponse(true, updatedSong);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }
}
