package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.CreateQuestionRequest;
import com.radio.rangrez.dto.QuestionDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.question.QuestionService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/questions")
@RestController
public class QuestionController {

    @Autowired
    private QuestionService questionService;

    @PostMapping
    public RestResponse createQuestion(@Valid @RequestBody CreateQuestionRequest request) {
        try {
            QuestionDto createdQuestion = questionService.createQuestion(request);
            return new RestResponse(true, createdQuestion);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping
    public RestResponse getAllQuestions() {
        try {
            List<QuestionDto> questions = questionService.getAllQuestions();
            return new RestResponse(true, questions);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/active")
    public RestResponse getActiveQuestions() {
        try {
            List<QuestionDto> questions = questionService.getActiveQuestions();
            return new RestResponse(true, questions);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getQuestionById(@PathVariable Long id) {
        try {
            QuestionDto question = questionService.getQuestionById(id);
            return new RestResponse(true, question);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public RestResponse updateQuestion(@PathVariable Long id, @Valid @RequestBody CreateQuestionRequest request) {
        try {
            QuestionDto updatedQuestion = questionService.updateQuestion(id, request);
            return new RestResponse(true, updatedQuestion);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteQuestion(@PathVariable Long id) {
        try {
            boolean deleted = questionService.deleteQuestion(id);
            return new RestResponse(deleted, deleted ? "Question deleted successfully" : "Failed to delete question");
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }


}
