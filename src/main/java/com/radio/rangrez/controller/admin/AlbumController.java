package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.AlbumDto;
import com.radio.rangrez.dto.CreateAlbumRequest;
import com.radio.rangrez.dto.SongDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.album.AlbumService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/albums")
@RestController
public class AlbumController {

    @Autowired
    private AlbumService albumService;

    @PostMapping
    public RestResponse createAlbum(@Valid @RequestBody CreateAlbumRequest request) {
        try {
            AlbumDto createdAlbum = albumService.createAlbum(request);
            return new RestResponse(true, createdAlbum);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping
    public RestResponse getAllAlbums() {
        try {
            List<AlbumDto> albums = albumService.getAllAlbums();
            return new RestResponse(true, albums);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getAlbumById(@PathVariable Long id) {
        try {
            AlbumDto album = albumService.getAlbumById(id);
            return new RestResponse(true, album);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public RestResponse updateAlbum(@PathVariable Long id, @Valid @RequestBody CreateAlbumRequest request) {
        try {
            AlbumDto updatedAlbum = albumService.updateAlbum(id, request);
            return new RestResponse(true, updatedAlbum);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteAlbum(@PathVariable Long id) {
        try {
            boolean deleted = albumService.deleteAlbum(id);
            return new RestResponse(deleted, deleted ? "Album deleted successfully" : "Failed to delete album");
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}/songs")
    public RestResponse getAlbumSongs(@PathVariable Long id) {
        try {
            List<SongDto> songs = albumService.getAlbumSongs(id);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PostMapping("/{albumId}/songs/{songId}")
    public RestResponse addSongToAlbum(@PathVariable Long albumId, @PathVariable Long songId, @RequestParam Integer trackNumber) {
        try {
            AlbumDto updatedAlbum = albumService.addSongToAlbum(albumId, songId, trackNumber);
            return new RestResponse(true, updatedAlbum);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @DeleteMapping("/{albumId}/songs/{songId}")
    public RestResponse removeSongFromAlbum(@PathVariable Long albumId, @PathVariable Long songId) {
        try {
            AlbumDto updatedAlbum = albumService.removeSongFromAlbum(albumId, songId);
            return new RestResponse(true, updatedAlbum);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/artist/{artistId}")
    public RestResponse getAlbumsByArtist(@PathVariable Long artistId) {
        try {
            List<AlbumDto> albums = albumService.getAlbumsByArtist(artistId);
            return new RestResponse(true, albums);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{albumId}/available-songs")
    public RestResponse getAvailableSongsForAlbum(@PathVariable Long albumId) {
        try {
            List<SongDto> availableSongs = albumService.getAvailableSongsForAlbum(albumId);
            return new RestResponse(true, availableSongs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }
}
