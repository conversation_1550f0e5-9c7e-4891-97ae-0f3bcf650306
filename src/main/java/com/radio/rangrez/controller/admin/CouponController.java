package com.radio.rangrez.controller.admin;

import com.radio.rangrez.dto.CreateCouponRequest;
import com.radio.rangrez.dto.CouponDto;
import com.radio.rangrez.dto.UpdateCouponPriorityRequest;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.coupon.CouponService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/admin/coupons")
@RestController
public class CouponController {

    @Autowired
    private CouponService couponService;

    @PostMapping
    public RestResponse createCoupon(@Valid @RequestBody CreateCouponRequest request) {
        try {
            CouponDto createdCoupon = couponService.createCoupon(request);
            return new RestResponse(true, createdCoupon);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping
    public RestResponse getAllCoupons() {
        try {
            List<CouponDto> coupons = couponService.getAllCoupons();
            return new RestResponse(true, coupons);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/active")
    public RestResponse getActiveCoupons() {
        try {
            List<CouponDto> coupons = couponService.getActiveCoupons();
            return new RestResponse(true, coupons);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/valid")
    public RestResponse getActiveAndValidCoupons() {
        try {
            List<CouponDto> coupons = couponService.getActiveAndValidCoupons();
            return new RestResponse(true, coupons);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/expired")
    public RestResponse getExpiredCoupons() {
        try {
            List<CouponDto> coupons = couponService.getExpiredCoupons();
            return new RestResponse(true, coupons);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getCouponById(@PathVariable Long id) {
        try {
            CouponDto coupon = couponService.getCouponById(id);
            return new RestResponse(true, coupon);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/code/{couponCode}")
    public RestResponse getCouponByCode(@PathVariable String couponCode) {
        try {
            CouponDto coupon = couponService.getCouponByCode(couponCode);
            return new RestResponse(true, coupon);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public RestResponse updateCoupon(@PathVariable Long id, @Valid @RequestBody CreateCouponRequest request) {
        try {
            CouponDto updatedCoupon = couponService.updateCoupon(id, request);
            return new RestResponse(true, updatedCoupon);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/{id}/priority")
    public RestResponse updateCouponPriority(@PathVariable Long id, @Valid @RequestBody UpdateCouponPriorityRequest request) {
        try {
            CouponDto updatedCoupon = couponService.updateCouponPriority(id, request);
            return new RestResponse(true, updatedCoupon);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public RestResponse deleteCoupon(@PathVariable Long id) {
        try {
            boolean deleted = couponService.deleteCoupon(id);
            return new RestResponse(deleted, deleted ? "Coupon deleted successfully" : "Failed to delete coupon");
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }
}
