package com.radio.rangrez.controller.event;

import com.radio.rangrez.dto.event.EventDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.event.EventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RequestMapping("/api/v1/public/events")
@RestController
public class PublicEventController {

    @Autowired
    private EventService eventService;

    @GetMapping
    public RestResponse getAllEvents() {
        try {
            List<EventDto> events = eventService.getAllEvents();
            return new RestResponse(true, events);
        } catch (Exception e) {
            return new RestResponse(false, "Error retrieving events: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getEventById(@PathVariable Long id) {
        try {
            EventDto event = eventService.getEventById(id);
            return new RestResponse(true, event);
        } catch (Exception e) {
            return new RestResponse(false, "Error retrieving event: " + e.getMessage());
        }
    }

    @GetMapping("/upcoming")
    public RestResponse getUpcomingEvents() {
        try {
            List<EventDto> events = eventService.getUpcomingEvents();
            return new RestResponse(true, events);
        } catch (Exception e) {
            return new RestResponse(false, "Error retrieving upcoming events: " + e.getMessage());
        }
    }

    @GetMapping("/between")
    public RestResponse getEventsBetweenDates(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        try {
            List<EventDto> events = eventService.getEventsBetweenDates(startDate, endDate);
            return new RestResponse(true, events);
        } catch (Exception e) {
            return new RestResponse(false, "Error retrieving events: " + e.getMessage());
        }
    }
}
