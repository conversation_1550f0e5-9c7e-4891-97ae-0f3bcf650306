package com.radio.rangrez.controller.playlist;

import com.radio.rangrez.dto.playlist.PlaylistQueryRequest;
import com.radio.rangrez.dto.playlist.PlaylistResponse;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.playlist.AIPlaylistService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/playlist")
@Slf4j
public class PlaylistController {

    @Autowired
    private AIPlaylistService aiPlaylistService;

    /**
     * Generate a playlist based on user query using AI
     * 
     * @param request The playlist query request containing user query
     * @return RestResponse containing the generated playlist
     */
    @PostMapping("/generate")
    public RestResponse generatePlaylist(@Valid @RequestBody PlaylistQueryRequest request) {
        try {
            log.info("Received playlist generation request: {}", request.getUserQuery());
            
            PlaylistResponse playlist = aiPlaylistService.generatePlaylistFromQuery(request);
            
            return new RestResponse(true, playlist);
            
        } catch (Exception e) {
            log.error("Error in playlist generation endpoint: {}", e.getMessage(), e);
            return new RestResponse(false, "Error generating playlist: " + e.getMessage());
        }
    }
}
