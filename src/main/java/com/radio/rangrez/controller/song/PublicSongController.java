package com.radio.rangrez.controller.song;

import com.radio.rangrez.dto.SongDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.song.SongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/public/songs")
@RestController
public class PublicSongController {

    @Autowired
    private SongService songService;

    @GetMapping
    public RestResponse getActiveSongs() {
        try {
            List<SongDto> songs = songService.getActiveSongs();
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/paginated")
    public RestResponse getActiveSongs(Pageable pageable) {
        try {
            Page<SongDto> songs = songService.getAllSongs(pageable);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public RestResponse getSongById(@PathVariable Long id) {
        try {
            SongDto song = songService.getSongById(id);
            return new RestResponse(true, song);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/featured")
    public RestResponse getFeaturedSongs() {
        try {
            List<SongDto> songs = songService.getFeaturedSongs();
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/popular")
    public RestResponse getPopularSongs() {
        try {
            List<SongDto> songs = songService.getPopularSongs();
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/top-rated")
    public RestResponse getTopRatedSongs() {
        try {
            List<SongDto> songs = songService.getTopRatedSongs();
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/search")
    public RestResponse searchSongs(@RequestParam String title) {
        try {
            List<SongDto> songs = songService.searchSongsByTitle(title);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/album/{albumName}")
    public RestResponse getSongsByAlbum(@PathVariable String albumName) {
        try {
            List<SongDto> songs = songService.getSongsByAlbum(albumName);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/language/{language}")
    public RestResponse getSongsByLanguage(@PathVariable String language) {
        try {
            List<SongDto> songs = songService.getSongsByLanguage(language);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/artist/{artistId}")
    public RestResponse getSongsByArtist(@PathVariable Long artistId) {
        try {
            List<SongDto> songs = songService.getSongsByArtist(artistId);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/genre/{genreId}")
    public RestResponse getSongsByGenre(@PathVariable Long genreId) {
        try {
            List<SongDto> songs = songService.getSongsByGenre(genreId);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/mood/{moodId}")
    public RestResponse getSongsByMood(@PathVariable Long moodId) {
        try {
            List<SongDto> songs = songService.getSongsByMood(moodId);
            return new RestResponse(true, songs);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PostMapping("/{id}/play")
    public RestResponse incrementPlayCount(@PathVariable Long id) {
        try {
            SongDto updatedSong = songService.incrementPlayCount(id);
            return new RestResponse(true, updatedSong);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PostMapping("/{id}/rate")
    public RestResponse rateSong(@PathVariable Long id, @RequestParam Double rating) {
        try {
            SongDto updatedSong = songService.rateSong(id, rating);
            return new RestResponse(true, updatedSong);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }
}
