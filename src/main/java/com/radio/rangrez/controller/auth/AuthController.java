package com.radio.rangrez.controller.auth;

import com.radio.rangrez.dto.UserDto;
import com.radio.rangrez.dto.auth.AuthResponse;
import com.radio.rangrez.dto.auth.CompleteProfileRequest;
import com.radio.rangrez.dto.auth.SendOtpRequest;
import com.radio.rangrez.dto.auth.VerifyOtpRequest;
import com.radio.rangrez.model.User;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.auth.AuthService;
import com.radio.rangrez.service.user.UserService;
import com.radio.rangrez.utils.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/api/v1/auth")
@RestController
public class AuthController {

    @Autowired
    private AuthService authService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @PostMapping("/send-otp")
    public RestResponse sendOtp(@Valid @RequestBody SendOtpRequest request) {
        try {
            boolean otpSent = authService.sendOtp(request);
            if (otpSent) {
                return new RestResponse(true, "OTP sent successfully to " + request.getEmail());
            } else {
                return new RestResponse(false, "Failed to send OTP");
            }
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PostMapping("/verify-otp")
    public RestResponse verifyOtp(@Valid @RequestBody VerifyOtpRequest request) {
        try {
            AuthResponse authResponse = authService.verifyOtp(request);
            return new RestResponse(true, authResponse);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PostMapping("/complete-profile")
    public RestResponse completeProfile(@Valid @RequestBody CompleteProfileRequest request, 
                                      HttpServletRequest httpRequest) {
        try {
            User user = userService.getCurrentUser();
            UserDto updatedUser = authService.completeProfile(user.getEmail(), request);
            return new RestResponse(true, updatedUser);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @PutMapping("/update-profile")
    public RestResponse updateProfile(@RequestBody CompleteProfileRequest request,
                                      HttpServletRequest httpRequest) {
        try {
            User user = userService.getCurrentUser();
            UserDto updatedUser = authService.updateProfile(user.getEmail(), request);
            return new RestResponse(true, updatedUser);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }

    @GetMapping("/profile")
    public RestResponse getUserProfile() {
        try {
            User user = userService.getCurrentUser();
            UserDto userProfile = authService.getUserProfile(user.getEmail());
            return new RestResponse(true, userProfile);
        } catch (Exception e) {
            return new RestResponse(false, "Error: " + e.getMessage());
        }
    }
}
