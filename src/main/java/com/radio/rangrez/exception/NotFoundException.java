package com.radio.rangrez.exception;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
public class NotFoundException extends BaseException {

    public NotFoundException(NotFoundType notFoundType) {
        super(404, notFoundType.getErrorKey(), notFoundType.getStatusCode(),  notFoundType.getErrorMessage(),
                notFoundType.getDeveloperMessage(), notFoundType.getDefaultMessage(), notFoundType.getDefaultMessageParamMap());
    }

    public NotFoundException(String message) {
        super(404, null, "404000", message, message, null, null);
    }

    @Getter
    public enum NotFoundType {
        ARTIST_NOT_FOUND("404001", "Artist not found", "Artist not found"),
        USER_NOT_FOUND("404002", "User not found", "User not found"),
        EVENT_NOT_FOUND("404003", "Event not found", "Event not found"),
        ALBUM_NOT_FOUND("404004", "Album not found", "Album not found"),
        SONG_NOT_FOUND("404005", "Song not found", "Song not found");


        private String errorKey;
        private final String statusCode;
        private final String errorMessage;
        private final String developerMessage;
        private String defaultMessage;
        private Map<String, String> defaultMessageParamMap;

        NotFoundType(String statusCode, String errorMessage, String developerMessage) {
            this.statusCode = statusCode;
            this.errorMessage = errorMessage;
            this.developerMessage = developerMessage;
        }
    }






}
