package com.radio.rangrez.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "songs")
@Entity
public class Song extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "title", nullable = false)
    private String title;

    @OneToMany(mappedBy = "song", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<SongArtist> songArtists = new ArrayList<>();

    @OneToMany(mappedBy = "song", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<SongGenre> songGenres = new ArrayList<>();

    @Column(name = "duration_seconds")
    private Integer durationSeconds;

    @Column(name = "audio_file_url")
    private String audioFileUrl;

    @Column(name = "cover_image_url")
    private String coverImageUrl;

    @Column(name = "album_name")
    private String albumName;

    @Column(name = "track_number")
    private Integer trackNumber;

    @Column(name = "release_date")
    private LocalDate releaseDate;

    @Column(name = "lyrics", columnDefinition = "text")
    private String lyrics;

    @Column(name = "language")
    private String language;

    @Column(name = "play_count")
    private Long playCount = 0L;

    @Column(name = "average_rating")
    private Double averageRating = 0.0;

    @Column(name = "total_ratings")
    private Long totalRatings = 0L;

    @Column(name = "is_explicit")
    private boolean isExplicit = false;

    @Column(name = "is_featured")
    private boolean isFeatured = false;
}
