package com.radio.rangrez.repository;

import com.radio.rangrez.model.Genre;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface GenreRepository extends JpaRepository<Genre, Long> {
    
    List<Genre> findByActivatedTrueOrderByDisplayOrderAscNameAsc();
    
    Optional<Genre> findByNameIgnoreCase(String name);
    
    boolean existsByNameIgnoreCase(String name);
    
    List<Genre> findByActivatedTrueOrderByDisplayOrderAsc();
}
